-r
/root/stf_exp/task-bench/cudastf_vs_openmp/openmp_system
--itt-config=frame
--stdsrc-config=cswitch,disk,pagefault
--ftrace-function-groups=iowait
--ftrace-config=freq,workq
--ftrace-function-groups=iowait
--collector=perf
--stack
--stackwalk=offline
--system-wide
--perf-threads=cpu
--perf-compression=1
--data-limit-mb=1000
--disk-space-limit=0
--mrte-type=java
--stack-unwind-limit=8388608
--event-config=CPU_CLK_UNHALTED.THREAD:sa=2900000,CPU_CLK_UNHALTED.REF_TSC:sample:sa=2900000,INST_RETIRED.ANY:sample:sa=2900000,CPU_CLK_UNHALTED.DISTRIBUTED:sa=2900000
--pmu-type
--uncore-sampling-interval=10
--mrte-mode=auto
--stdsrc-config=cswitch
--stack-size=1024
--p-state-trigger=CPU_CLK_UNHALTED.REF_TSC
--
../openmp/main
-steps
5
-width
4
-type
stencil_1d
-kernel
compute_bound
