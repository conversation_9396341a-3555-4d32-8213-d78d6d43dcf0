<?xml version="1.0" encoding="UTF-8"?>
<bag xmlns:boolean="http://www.w3.org/2001/XMLSchema#boolean" xmlns:exsl="http://exslt.org/common" xmlns:str="http://exslt.org/strings" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
 <internal>
  <name>%SystemOverviewAtypeName</name>
  <description>%SystemOverviewAtypeDescription</description>
  <shortDescription>%SystemOverviewAtypeShortDescription</shortDescription>
  <abbreviation>so</abbreviation>
  <alias>system-overview</alias>
  <iconClass>graph-chart</iconClass>
  <property name="helpId">intel.phe.configs.system_overview_f1239</property>
 </internal>
 <prerequisites xsl:version="1.0" exsl:keep_exsl_namespace="" syntax="norules">
  <xsl:variable name="pmuConditionsExtended" select="document('config://include/pmu_variables_extended.xsl')"/>
  <xsl:variable name="isExtendedIsaSupport" select="$pmuConditionsExtended//variables/isExtendedIsaSupport"/>
  <xsl:if test="not (exsl:ctx('targetOS') = 'Android' or exsl:ctx('targetOS') = 'Linux' or exsl:ctx('targetOS') = 'Windows') or $isExtendedIsaSupport='true'">
   <xsl:value-of select="exsl:error('%ThisAnalysisTypeIsNotApplicable')"/>
  </xsl:if>
  <xsl:copy-of select="document('config://analysis_type/include/require_connection.xsl?connections=group_generic')"/>
 </prerequisites>
 <knobs xsl:version="1.0" exsl:keep_exsl_namespace="" syntax="norules">
  <stringKnob id="initialViewpoint" displayName="%InitialViewpoint" boolean:visible="false">
   <defaultValue>%SystemOverviewViewpointName</defaultValue>
  </stringKnob>
  <xsl:variable name="commonKnobs" select="document('config://analysis_type/include/knobs.xsl')"/>
  <xsl:choose>
   <xsl:when test="exsl:ctx('targetOS', '') = 'Linux' or exsl:ctx('targetOS', '') = 'Android' or         (exsl:ctx('targetOS', '') = 'Windows' and exsl:is_experimental('windows-hw-tracing'))">
    <groupKnob id="groupForCustomControl">
     <knobProperty name="knob_control_id">systemOverviewGroup</knobProperty>
     <knobs>
      <enumKnob id="collectingMode" cliName="collecting-mode" boolean:visible="true">
       <values>
        <value displayName="%SoHardwareTracing" cliName="hw-tracing">hwTracing</value>
        <value displayName="%SoHardwareSampling" cliName="hw-sampling">hwSampling</value>
        <defaultValue>hwSampling</defaultValue>
       </values>
      </enumKnob>
      <xsl:copy-of select="$commonKnobs//knobs/doubleKnob[@id='samplingInterval']"/>
     </knobs>
    </groupKnob>
    <xsl:if test="exsl:ctx('targetOS', '') != 'Windows'">
     <booleanKnob id="enableInterrupts" displayName="%EnableInterruptsCollection" cliName="enable-interrupt-collection">
      <boolean:defaultValue>false</boolean:defaultValue>
      <description>%EnableInterruptsCollectionDescription</description>
     </booleanKnob>
    </xsl:if>
   </xsl:when>
   <xsl:otherwise>
    <enumKnob id="collectingMode" cliName="collecting-mode" boolean:visible="false">
     <values>
      <value displayName="%SoHardwareSampling" cliName="hw-sampling">hwSampling</value>
      <defaultValue>hwSampling</defaultValue>
     </values>
    </enumKnob>
    <xsl:copy-of select="$commonKnobs//knobs/doubleKnob[@id='samplingInterval']"/>
   </xsl:otherwise>
  </xsl:choose>
  <xsl:if test="exsl:ctx('targetOS', '') = 'Linux' or exsl:ctx('targetOS', '') = 'Windows'">
   <booleanKnob id="analyzePowerUsage" displayName="%AnalyzePowerUsage" cliName="analyze-power-usage">
    <boolean:defaultValue>false</boolean:defaultValue>
    <description>%AnalyzePowerUsageDescription</description>
   </booleanKnob>
   <booleanKnob id="analyzeThrottlingReasons" displayName="%AnalyzeThrottlingReasons" cliName="analyze-throttling-reasons" boolean:visible="true">
    <description>%AnalyzeThrottlingReasonsDescription</description>
    <boolean:defaultValue>false</boolean:defaultValue>
   </booleanKnob>
  </xsl:if>
 </knobs>
 <analysis xsl:version="1.0" exsl:keep_exsl_namespace="" str:keep_str_namespace="" syntax="norules">
  <collector id="runsa">
   <xsl:if test="exsl:ctx('platformType', '0') = '146'">
    <xsl:value-of select="exsl:error('%UnknownPMUForAT')"/>
   </xsl:if>
   <xsl:if test="exsl:ctx('analyzeThrottlingReasons', 0) and not(exsl:ctx('isCpuThrottlingAvailable', '0'))">
    <xsl:value-of select="exsl:error('%CPUThrottlingUnavailable')"/>
   </xsl:if>
   <xsl:choose>
    <xsl:when test="exsl:ctx('collectingMode', 'hwSampling')='hwSampling'">
     <xsl:variable name="events" select="document('config://analysis_type/include/common_events.xsl')"/>
     <collectorKnob knob="pmuEventConfig">
      <xsl:value-of select="$events//events/cpi"/>
     </collectorKnob>
     <collectorKnob knob="eventMode">
      <xsl:copy-of select="exsl:ctx('eventCollectionMode', 'all')"/>
     </collectorKnob>
     <boolean:collectorKnob knob="enableStackCollection">true</boolean:collectorKnob>
     <xsl:variable name="pmuConditions" select="document('config://include/pmu_variables.xsl')"/>
     <xsl:variable name="isLBRCallStackAvailable" select="$pmuConditions//variables/isLbrStackAvailable"/>
     <xsl:if test="$isLBRCallStackAvailable='true'">
      <collectorKnob knob="stackTypeCollect">lbr</collectorKnob>
      <collectorKnob knob="preferredEbsCollectorOrder">sep,perf</collectorKnob>
     </xsl:if>
     <xsl:if test="exsl:ctx('targetOS') = 'Android'">
      <collectorKnob knob="atraceEventConfig">input,view,webview,audio,video,camera,hal,res,dalvik</collectorKnob>
      <collectorKnob knob="ftraceEventConfig">freq,workq,sync,disk,filesystem</collectorKnob>
     </xsl:if>
     <xsl:if test="exsl:ctx('targetOS') = 'Linux'">
      <collectorKnob knob="ftraceEventConfig">freq,workq</collectorKnob>
     </xsl:if>
     <collectorKnob knob="pmuSamplingInterval">
      <xsl:value-of select="format-number(exsl:ctx('samplingInterval', 1), '#.####')"/>
     </collectorKnob>
     <xsl:if test="exsl:ctx('enableInterrupts', 0)">
      <xsl:if test="(exsl:ctx('targetOS','')='Android' or (exsl:ctx('targetOS','') = 'Linux'))">
       <xsl:for-each select="str:tokenize(exsl:ctx('isFtraceAvailable',''), ',')">
        <xsl:variable name="fraceStatus">
         <xsl:choose>
          <xsl:when test=".='debugfsNotExists'">%DebugFSExistenceError</xsl:when>
          <xsl:when test=".='debugfsNotAccessible'">%DebugFSAccessError</xsl:when>
          <xsl:when test=".='debugfsOffInConfigs'">%DebugFSConfigError</xsl:when>
          <xsl:when test=".='debugfsIsNotValidFsType'">%DebugFSInvalidTypeError</xsl:when>
          <xsl:when test=".='ftraceAccessError'">
           <xsl:choose>
            <xsl:when test="not(contains(exsl:ctx('isFtraceAvailable',''), 'debugfs'))">%FtraceAccessError</xsl:when>
            <xsl:otherwise>notErrorOrWarning</xsl:otherwise>
           </xsl:choose>
          </xsl:when>
          <xsl:when test=".='ftraceConfigError'">%FtraceConfigError</xsl:when>
          <xsl:when test=".='ftraceUnknownError'">%FtraceUnknownError</xsl:when>
          <xsl:when test=".='yes'">yes</xsl:when>
          <xsl:otherwise>yes</xsl:otherwise>
         </xsl:choose>
        </xsl:variable>
        <xsl:if test="( $fraceStatus != 'yes' and $fraceStatus != 'notErrorOrWarning')">
         <xsl:value-of select="exsl:error('%FtraceIsNotAvailable')"/>
         <xsl:value-of select="exsl:error($fraceStatus)"/>
        </xsl:if>
       </xsl:for-each>
      </xsl:if>
     </xsl:if>
     <boolean:collectorKnob knob="errorsAsWarnings">true</boolean:collectorKnob>
     <boolean:collectorKnob knob="gpuUsage">true</boolean:collectorKnob>
     <boolean:collectorKnob knob="systemWideContextSwitch">true</boolean:collectorKnob>
     <xsl:if test="exsl:ctx('Hypervisor', 'None') = 'None' or (exsl:ctx('Hypervisor', 'None') = 'Microsoft Hv' and exsl:ctx('HypervisorType', 'None') = 'Hyper-V')">
      <boolean:collectorKnob knob="collectMemBandwidth">true</boolean:collectorKnob>
      <boolean:collectorKnob knob="collectPCIeBandwidth">true</boolean:collectorKnob>
     </xsl:if>
     <boolean:collectorKnob knob="collectIoWaits">true</boolean:collectorKnob>
     <boolean:collectorKnob knob="systemWideDiskIO">true</boolean:collectorKnob>
     <collectorKnob knob="enableInterruptsCollection">
      <xsl:value-of select="exsl:ctx('enableInterrupts', 0)"/>
     </collectorKnob>
    </xsl:when>
    <xsl:otherwise>
     <boolean:collectorKnob knob="collectHwTrace">true</boolean:collectorKnob>
     <xsl:choose>
      <xsl:when test="exsl:ctx('isSEPDriverAvailable', 0)">
       <collectorKnob knob="preferredEbsCollectorOrder">sep,perf</collectorKnob>
      </xsl:when>
      <xsl:otherwise>
       <xsl:if test="exsl:ctx('targetOS','') = 'Windows'">
        <xsl:value-of select="exsl:error('%SepDriverUnavailableWindows')"/>
       </xsl:if>
       <boolean:collectorKnob knob="usePerf">true</boolean:collectorKnob>
      </xsl:otherwise>
     </xsl:choose>
    </xsl:otherwise>
   </xsl:choose>
   <xsl:if test="exsl:ctx('analyzePowerUsage', 0)">
    <collectorKnob knob="analyzeEnergyConsumption">true</collectorKnob>
   </xsl:if>
   <xsl:if test="exsl:ctx('analyzeThrottlingReasons', 0)">
    <collectorKnob knob="collectThrottlingReasons">true</collectorKnob>
   </xsl:if>
   <collectorKnob knob="supportedTargetTypes">all</collectorKnob>
   <boolean:collectorKnob knob="forceSystemWide">true</boolean:collectorKnob>
  </collector>
 </analysis>
</bag>
