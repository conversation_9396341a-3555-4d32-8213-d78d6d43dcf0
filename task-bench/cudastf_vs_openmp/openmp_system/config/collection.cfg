<?xml version='1.0' encoding='UTF-8'?>

<bag xmlns:boolean="http://www.w3.org/2001/XMLSchema#boolean" xmlns:double="http://www.intel.com/2001/XMLSchema#double" xmlns:int="http://www.w3.org/2001/XMLSchema#int" xmlns:null="http://www.intel.com/2009/BagSchema#null">
 <pointer:workload pointer:type_id="dasID_cctrl2::ApplicationWorkload" pointer:ptr_type="dasID_cctrl2::IWorkload">
  <workload targetName="launch" launch_app.app_to_launch="../openmp/main" launch_app.app_parameters="&quot;-steps&quot; &quot;5&quot; &quot;-width&quot; &quot;4&quot; &quot;-type&quot; &quot;stencil_1d&quot; &quot;-kernel&quot; &quot;compute_bound&quot; " result_directory.path="/root/stf_exp/task-bench/cudastf_vs_openmp/openmp_system">
   <search_directories/>
   <context>
    <contextValue id="allowMultipleRuns" boolean:value="false"/>
    <contextValue id="analyzeKvmGuest" boolean:value="false"/>
    <contextValue id="analyzeSystemWide" boolean:value="false"/>
    <contextValue id="cpuMask" value=""/>
    <contextValue id="customCollector" value=""/>
    <contextValue id="dataLimit" int:value="1000"/>
    <contextValue id="enableRing" boolean:value="false"/>
    <contextValue id="finalizationMode" value="fast"/>
    <contextValue id="followChild" boolean:value="true"/>
    <contextValue id="followChildGroup" null:value=""/>
    <contextValue id="followChildStrategy" value=""/>
    <contextValue id="groupForFinalizationControl" null:value=""/>
    <contextValue id="kvmGuestKallsyms" value=""/>
    <contextValue id="kvmGuestModules" value=""/>
    <contextValue id="kvmProfileGuest" null:value=""/>
    <contextValue id="mrteMode" value="auto"/>
    <contextValue id="targetDurationType" value="short"/>
    <contextValue id="targetRingBuffer" double:value="0"/>
    <contextValue id="targetType" value="launch"/>
    <contextValue id="traceMpi" boolean:value="false"/>
    <contextValue id="tracingMode" null:value=""/>
    <contextValue id="wrapperScriptContent" value=""/>
    <contextValue id="wrapperScriptPath" value=""/>
   </context>
  </workload>
 </pointer:workload>
</bag>
