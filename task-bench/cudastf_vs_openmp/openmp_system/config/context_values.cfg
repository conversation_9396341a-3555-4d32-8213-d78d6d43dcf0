<?xml version="1.0" encoding="UTF-8"?>
<bag xmlns:boolean="http://www.w3.org/2001/XMLSchema#boolean" xmlns:double="http://www.intel.com/2001/XMLSchema#double" xmlns:int="http://www.w3.org/2001/XMLSchema#int" xmlns:null="http://www.intel.com/2009/BagSchema#null" xmlns:unsignedByte="http://www.w3.org/2001/XMLSchema#unsignedByte" xmlns:unsignedLong="http://www.w3.org/2001/XMLSchema#unsignedLong" xmlns:unsignedShort="http://www.w3.org/2001/XMLSchema#unsignedShort">
 <contextValue id="CLIENT_ID" value="CLI"/>
 <contextValue id="CPU_NAME" value="Unknown"/>
 <contextValue id="GPUUserGroups" value=""/>
 <contextValue id="Hypervisor" value="None"/>
 <contextValue id="HypervisorType" value="None"/>
 <contextValue id="IsNUMANodeWithoutCPUsPresent" boolean:value="false"/>
 <contextValue id="L3CATDetails" value="COS=16;ways=11"/>
 <contextValue id="LLCSize" unsignedLong:value="23068672"/>
 <contextValue id="LinuxPerfCapabilities" value="breakpoint:raw;cpu:raw,format,events,ldlat,frontend,offcore_rsp;cstate_core:raw,format,events;cstate_pkg:raw,format,events;intel_bts:raw;intel_pt:raw,format;kprobe:raw,format;msr:raw,format,events;power:raw,format,events;software:raw;tracepoint:raw;uncore_cha:16,raw,format;uncore_iio:6,raw,format;uncore_iio_free_running:6,raw,format,events;uncore_imc:6,raw,format,events;uncore_irp:6,raw,format;uncore_m2m:2,raw,format;uncore_m2pcie:3,raw,format;uncore_m3upi:3,raw,format;uncore_pcu:raw,format;uncore_ubox:raw,format;uncore_upi:3,raw,format;uprobe:raw,format"/>
 <contextValue id="LinuxPerfCredentials" value="Unlimited"/>
 <contextValue id="LinuxPerfMuxIntervalMs" int:value="4"/>
 <contextValue id="LinuxPerfStackCapabilities" value="fp,dwarf,lbr"/>
 <contextValue id="LinuxRelease" value="5.19.0-47-generic"/>
 <contextValue id="OS" value="Linux"/>
 <contextValue id="OSBitness" value="64"/>
 <contextValue id="OSBuildNumber" unsignedShort:value="0"/>
 <contextValue id="PMU" value=""/>
 <contextValue id="PerfmonVersion" unsignedByte:value="4"/>
 <contextValue id="RootPrivileges" boolean:value="true"/>
 <contextValue id="adjustCollectionBoundsByOMPApps" boolean:value="false"/>
 <contextValue id="advancedLoopAnalysis" boolean:value="false"/>
 <contextValue id="allowMultipleRuns" boolean:value="false"/>
 <contextValue id="analyzeActivePowerConsumption" boolean:value="false"/>
 <contextValue id="analyzeDgfxBandwidth" boolean:value="false"/>
 <contextValue id="analyzeEHFIClasses" boolean:value="false"/>
 <contextValue id="analyzeEnergyConsumption" boolean:value="false"/>
 <contextValue id="analyzeFPU" boolean:value="false"/>
 <contextValue id="analyzeFullProcTrace" boolean:value="false"/>
 <contextValue id="analyzeIdlePowerConsumption" boolean:value="false"/>
 <contextValue id="analyzeKvmGuest" boolean:value="false"/>
 <contextValue id="analyzeMemoryConsumption" boolean:value="false"/>
 <contextValue id="analyzePersistentMemory" boolean:value="false"/>
 <contextValue id="analyzePowerUsage" boolean:value="false"/>
 <contextValue id="analyzeSystemWide" boolean:value="false"/>
 <contextValue id="analyzeThrottlingReasons" boolean:value="false"/>
 <contextValue id="apsMode" boolean:value="false"/>
 <contextValue id="areKernelPtrsRestricted" value="no"/>
 <contextValue id="atraceEventConfig" value=""/>
 <contextValue id="basicBlockAnalysis" boolean:value="true"/>
 <contextValue id="cacheMonitoringUpscalingFactor" unsignedLong:value="65536"/>
 <contextValue id="calleeAttributionMode" value="undefined"/>
 <contextValue id="chipsetEventConfig" value=""/>
 <contextValue id="collectCAT" boolean:value="false"/>
 <contextValue id="collectCPUGPUBandwidth" boolean:value="false"/>
 <contextValue id="collectCacheOccupancy" boolean:value="false"/>
 <contextValue id="collectCallCounts" boolean:value="false"/>
 <contextValue id="collectFPGAOpenCl" boolean:value="false"/>
 <contextValue id="collectFpgaBlueStreamEvents" boolean:value="false"/>
 <contextValue id="collectFramesMode" boolean:value="true"/>
 <contextValue id="collectFullProcTrace" boolean:value="false"/>
 <contextValue id="collectGpuCm" boolean:value="false"/>
 <contextValue id="collectGpuMetal" boolean:value="false"/>
 <contextValue id="collectGpuOpenCl" value=""/>
 <contextValue id="collectGpuOpenClArg" value=""/>
 <contextValue id="collectHwTrace" boolean:value="false"/>
 <contextValue id="collectIOMMU" boolean:value="false"/>
 <contextValue id="collectIoMode" value="off"/>
 <contextValue id="collectIoWaits" boolean:value="true"/>
 <contextValue id="collectL3ExternalBW" boolean:value="false"/>
 <contextValue id="collectMemBandwidth" boolean:value="true"/>
 <contextValue id="collectMemObjects" boolean:value="false"/>
 <contextValue id="collectMmioAccess" boolean:value="false"/>
 <contextValue id="collectOpenMPRegions" boolean:value="false"/>
 <contextValue id="collectOsCounters" boolean:value="false"/>
 <contextValue id="collectPCIeBandwidth" boolean:value="true"/>
 <contextValue id="collectPCIeP2PBandwidth" boolean:value="false"/>
 <contextValue id="collectPStateData" boolean:value="true"/>
 <contextValue id="collectPTforTSX" boolean:value="false"/>
 <contextValue id="collectPreciseClockticks" boolean:value="false"/>
 <contextValue id="collectSWHotspots" boolean:value="false"/>
 <contextValue id="collectTSXCycles" boolean:value="false"/>
 <contextValue id="collectThrottlingReasons" boolean:value="false"/>
 <contextValue id="collectTopology" boolean:value="false"/>
 <contextValue id="collectTripCounts" boolean:value="false"/>
 <contextValue id="collectUserDataAllMode" boolean:value="false"/>
 <contextValue id="collectUserTasksEventsCountersMode" boolean:value="false"/>
 <contextValue id="collectXpuCompute" value="true"/>
 <contextValue id="collectingMode" value="hwSampling"/>
 <contextValue id="connectionType" value="localhost"/>
 <contextValue id="cpuByIoWaits" boolean:value="false"/>
 <contextValue id="cpuGpuUsageData" boolean:value="false"/>
 <contextValue id="cpuMask" value=""/>
 <contextValue id="createGPUQueueFrames" boolean:value="false"/>
 <contextValue id="cswitchMode" value="inactive"/>
 <contextValue id="customCollector" value=""/>
 <contextValue id="dataLimit" int:value="1000"/>
 <contextValue id="disableGPUSysinfo" boolean:value="false"/>
 <contextValue id="disableRetCompression" boolean:value="false"/>
 <contextValue id="dramBandwidthLimits" boolean:value="false"/>
 <contextValue id="emonCSVTraceFormat" boolean:value="false"/>
 <contextValue id="emonMachineReadableVersionFile" boolean:value="false"/>
 <contextValue id="emonPreset" value=""/>
 <contextValue id="enableCStateCollection" boolean:value="false"/>
 <contextValue id="enableCSwitch" boolean:value="false"/>
 <contextValue id="enableCycleAccurateMode" boolean:value="false"/>
 <contextValue id="enableDramBandwidthLimitsWarning" boolean:value="false"/>
 <contextValue id="enableHWBasedCSCollection" boolean:value="false"/>
 <contextValue id="enableInterrupts" boolean:value="false"/>
 <contextValue id="enableInterruptsCollection" boolean:value="false"/>
 <contextValue id="enableLBRCollection" boolean:value="false"/>
 <contextValue id="enableMemoryObjectCorrelation" boolean:value="false"/>
 <contextValue id="enableMpiTracing" boolean:value="false"/>
 <contextValue id="enableOpenglesInstrumentation" boolean:value="false"/>
 <contextValue id="enablePEBSCollection" boolean:value="false"/>
 <contextValue id="enableParallelFsCollection" boolean:value="false"/>
 <contextValue id="enableRing" boolean:value="false"/>
 <contextValue id="enableRing0ProfilingMode" boolean:value="false"/>
 <contextValue id="enableStackCollection" boolean:value="true"/>
 <contextValue id="enableThreadAffinity" boolean:value="false"/>
 <contextValue id="enableTimedPEBSCollection" boolean:value="false"/>
 <contextValue id="enableVTSSCollection" boolean:value="false"/>
 <contextValue id="energyProfilingMode" value="none"/>
 <contextValue id="errorsAsWarnings" boolean:value="true"/>
 <contextValue id="eventInfo" boolean:value="false"/>
 <contextValue id="eventMode" value="all"/>
 <contextValue id="eventMuxFactor" int:value="5"/>
 <contextValue id="explicitUncoreEventsConfig" boolean:value="false"/>
 <contextValue id="fileRequiestLogic" value="local"/>
 <contextValue id="finalizationMode" value="fast"/>
 <contextValue id="followChild" boolean:value="true"/>
 <contextValue id="followChildGroup" null:value=""/>
 <contextValue id="followChildStrategy" value=""/>
 <contextValue id="forceMuxOff" boolean:value="false"/>
 <contextValue id="forceShowInlines" boolean:value="false"/>
 <contextValue id="forceSystemWide" boolean:value="true"/>
 <contextValue id="fpgaAocxOrBinaryFile" value=""/>
 <contextValue id="fpgaNoMemTransfers" boolean:value="false"/>
 <contextValue id="fpgaNoTemporal" boolean:value="false"/>
 <contextValue id="fpgaOnBoard" value="None"/>
 <contextValue id="fpgaPeriod" int:value="0"/>
 <contextValue id="fpgaSourceFile" value=""/>
 <contextValue id="fpuVersion" value="1_0"/>
 <contextValue id="ftraceEventConfig" value="freq,workq"/>
 <contextValue id="genArchOnBoard" int:value="0"/>
 <contextValue id="gpuAdapterNames" value=""/>
 <contextValue id="gpuCounters" value=""/>
 <contextValue id="gpuFixedMetricsSelected" value=""/>
 <contextValue id="gpuHWProfiling" value=""/>
 <contextValue id="gpuMetricsSelected" value=""/>
 <contextValue id="gpuProfilingMode" value=""/>
 <contextValue id="gpuSamplingInterval" value=""/>
 <contextValue id="gpuUsage" boolean:value="true"/>
 <contextValue id="groupForCustomControl" null:value=""/>
 <contextValue id="groupForFinalizationControl" null:value=""/>
 <contextValue id="groupForGPUCustomCollection" null:value=""/>
 <contextValue id="groupForTraceEventConfig" null:value=""/>
 <contextValue id="handleLostEvents" boolean:value="false"/>
 <contextValue id="hideSystemByDefault" boolean:value="false"/>
 <contextValue id="hideWarningInPerfsnapshot" boolean:value="false"/>
 <contextValue id="hostName" value="amax"/>
 <contextValue id="hostOS" value="Linux"/>
 <contextValue id="i915Status" value="MissingDriver"/>
 <contextValue id="ignorePowerData" boolean:value="false"/>
 <contextValue id="inKernelProfilingAnalysis" boolean:value="false"/>
 <contextValue id="initialViewpoint" value="%SystemOverviewViewpointName"/>
 <contextValue id="iptCollectEvents" boolean:value="false"/>
 <contextValue id="iptRegionsToLoad" int:value="0"/>
 <contextValue id="is3DXP2LMMode" boolean:value="false"/>
 <contextValue id="is3DXPAppDirectMode" boolean:value="false"/>
 <contextValue id="is3DXPPresent" boolean:value="false"/>
 <contextValue id="isAOCLAvailable" boolean:value="false"/>
 <contextValue id="isCATSupportedByCPU" boolean:value="true"/>
 <contextValue id="isCPUSupportedBySocwatch" boolean:value="true"/>
 <contextValue id="isCSwitchAvailable" value="no"/>
 <contextValue id="isCpuThrottlingAvailable" boolean:value="true"/>
 <contextValue id="isDeviceOrCredentialGuardEnabled" boolean:value="false"/>
 <contextValue id="isEHFIAvailable" boolean:value="false"/>
 <contextValue id="isEnergyCollectionSupported" boolean:value="true"/>
 <contextValue id="isFtraceAvailable" value="ftraceAccessError,debugfsNotExists"/>
 <contextValue id="isFtraceAvailableKnob" value="ftraceAccessError,debugfsNotExists"/>
 <contextValue id="isFunctionTracingAvailable" value="no"/>
 <contextValue id="isGpuBusynessAvailable" value="unsupportedHardware"/>
 <contextValue id="isGpuMultiRunRequired" boolean:value="false"/>
 <contextValue id="isGpuWaitAvailable" value="no"/>
 <contextValue id="isHTEnabled" boolean:value="true"/>
 <contextValue id="isIowaitTracingAvailable" value="no"/>
 <contextValue id="isL2CATAvailable" boolean:value="false"/>
 <contextValue id="isL3CATAvailable" boolean:value="true"/>
 <contextValue id="isL3CacheOccupancyAvailable" boolean:value="true"/>
 <contextValue id="isL3LocalBWAvailable" boolean:value="true"/>
 <contextValue id="isL3MonitoringSupportedByCPU" boolean:value="true"/>
 <contextValue id="isL3TotalBWAvailable" boolean:value="true"/>
 <contextValue id="isMaxDRAMBandwidthMeasurementSupported" boolean:value="true"/>
 <contextValue id="isMdfEtwAvailable" boolean:value="false"/>
 <contextValue id="isNMIWatchDogTimerRunning" boolean:value="true"/>
 <contextValue id="isPAXDriverLoaded" boolean:value="false"/>
 <contextValue id="isPStateAvailable" boolean:value="true"/>
 <contextValue id="isPTAvailable" boolean:value="true"/>
 <contextValue id="isPerfPCIeMappingAvailable" boolean:value="true"/>
 <contextValue id="isPowerVRDataAvailable" value="no"/>
 <contextValue id="isPtraceAvailable" boolean:value="true"/>
 <contextValue id="isPtraceScopeLimited" boolean:value="false"/>
 <contextValue id="isPytraceAvailable" boolean:value="true"/>
 <contextValue id="isSEPDriverAvailable" boolean:value="false"/>
 <contextValue id="isSGXAvailable" boolean:value="false"/>
 <contextValue id="isSocwatchDriverLoaded" boolean:value="false"/>
 <contextValue id="isTPSSAvailable" boolean:value="true"/>
 <contextValue id="isTSXAvailable" boolean:value="false"/>
 <contextValue id="isUArchUsageAvailable" boolean:value="false"/>
 <contextValue id="isVSyncAvailable" value="no"/>
 <contextValue id="isVTSSPPDriverAvailable" boolean:value="false"/>
 <contextValue id="isXelinkAvailable" boolean:value="false"/>
 <contextValue id="kernelsToProfile" value="*#1#1#4294967295"/>
 <contextValue id="kvmGuestKallsyms" value=""/>
 <contextValue id="kvmGuestModules" value=""/>
 <contextValue id="kvmProfileGuest" null:value=""/>
 <contextValue id="l0DevicesAvailable" boolean:value="false"/>
 <contextValue id="l0GPUDevicesAvailable" boolean:value="false"/>
 <contextValue id="l0LoaderStatus" value="LibNotFound"/>
 <contextValue id="l0MetricConfig" value=""/>
 <contextValue id="l0SamplingInterval" double:value="1"/>
 <contextValue id="l0SamplingType" value="time"/>
 <contextValue id="l0SysmanConfig" value=""/>
 <contextValue id="l0VPUDevicesAvailable" boolean:value="false"/>
 <contextValue id="loadLbrStackToDb" boolean:value="true"/>
 <contextValue id="loadPebsData" boolean:value="true"/>
 <contextValue id="loadRawLbrData" boolean:value="false"/>
 <contextValue id="maxRegionDuration" double:value="100"/>
 <contextValue id="memoryAccessBandwidthMeasuring" boolean:value="false"/>
 <contextValue id="memoryObjectMinSize" int:value="1024"/>
 <contextValue id="memoryType" value="Unknown"/>
 <contextValue id="mrteMode" value="auto"/>
 <contextValue id="mrteType" value="java"/>
 <contextValue id="nameThreadsAsCreationModule" boolean:value="false"/>
 <contextValue id="numaVersionCurrent" value="1_0"/>
 <contextValue id="omniPathOnBoard" value="None"/>
 <contextValue id="openclSourceAsm" boolean:value="true"/>
 <contextValue id="pciClassParts" value="0x1:0,1;0x2:2;0x3:0,1;0x4:0;0x6:0,1,2;0x8:0;"/>
 <contextValue id="perfForceSystemWide" boolean:value="false"/>
 <contextValue id="platformType" value="0"/>
 <contextValue id="pmuEventConfig" value="CPU_CLK_UNHALTED.THREAD:sa=2900000,CPU_CLK_UNHALTED.REF_TSC:sample:sa=2900000,INST_RETIRED.ANY:sample:sa=2900000,CPU_CLK_UNHALTED.DISTRIBUTED:sa=2900000"/>
 <contextValue id="pmuSamplingInterval" double:value="1"/>
 <contextValue id="populatedIoParts" value="0;1;2"/>
 <contextValue id="populatedIoUnits" value="0;1;2;3"/>
 <contextValue id="populatedTidValuesForIO" value="0x1d8;0x1f0;0x1f8"/>
 <contextValue id="preciseMultiplexing" boolean:value="false"/>
 <contextValue id="preferDriverlessCollection" boolean:value="true"/>
 <contextValue id="preferedGpuAdapter" value="none"/>
 <contextValue id="preferredEbsCollectorOrder" value="perf,vtss,sep"/>
 <contextValue id="processKernelBinaries" boolean:value="false"/>
 <contextValue id="referenceFrequency" unsignedLong:value="2900000000"/>
 <contextValue id="resolveCallsites" boolean:value="true"/>
 <contextValue id="restrictPCIeBandwidthByClass" value="None"/>
 <contextValue id="ringBuffer" int:value="0"/>
 <contextValue id="runsa:enable" boolean:value="true"/>
 <contextValue id="samplingInterval" double:value="1"/>
 <contextValue id="showGPUBandwidthHistogram" boolean:value="true"/>
 <contextValue id="showInlinesByDefault" boolean:value="true"/>
 <contextValue id="stackSize" int:value="1024"/>
 <contextValue id="stackTypeCollect" value="software"/>
 <contextValue id="stackUnwindLimit" int:value="8388608"/>
 <contextValue id="stallReasonsSamplingInterval" value=""/>
 <contextValue id="supportedTargetTypes" value="all"/>
 <contextValue id="suppressCSVSyntaxWarnings" boolean:value="false"/>
 <contextValue id="systemCollectorConfig" value=""/>
 <contextValue id="systemWideContextSwitch" boolean:value="true"/>
 <contextValue id="systemWideDiskIO" boolean:value="true"/>
 <contextValue id="targetDurationType" value="short"/>
 <contextValue id="targetGPU" value="none"/>
 <contextValue id="targetOS" value="Linux"/>
 <contextValue id="targetOption" value="localhost"/>
 <contextValue id="targetRingBuffer" double:value="0"/>
 <contextValue id="targetType" value="launch"/>
 <contextValue id="tidValuesForIO" value="0x1d8;0x1f0;0x1f8"/>
 <contextValue id="tmamVersionCurrent" value="5_0"/>
 <contextValue id="traceMpi" boolean:value="false"/>
 <contextValue id="tracingMode" null:value=""/>
 <contextValue id="uncoreSamplingInterval" int:value="10"/>
 <contextValue id="useAOCLProfile" boolean:value="false"/>
 <contextValue id="useAggregatedCounting" boolean:value="false"/>
 <contextValue id="useCountingMode" boolean:value="false"/>
 <contextValue id="useEventBasedCounts" boolean:value="false"/>
 <contextValue id="useGpuCounting" boolean:value="false"/>
 <contextValue id="usePerf" boolean:value="false"/>
 <contextValue id="usePerfMetrics" boolean:value="false"/>
 <contextValue id="wrapperScriptContent" value=""/>
 <contextValue id="wrapperScriptPath" value=""/>
</bag>
