#!/bin/bash

# Master script for complete CUDASTF vs OpenMP analysis
# Runs benchmarks, collects data, and generates comprehensive analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}  CUDASTF vs OpenMP Complete Analysis  ${NC}"
echo -e "${CYAN}========================================${NC}"
echo

# Check if we're in the right directory
if [[ ! -d "scripts" ]] || [[ ! -d "analysis" ]]; then
    echo -e "${RED}Error: Please run this script from the cudastf_vs_openmp directory${NC}"
    exit 1
fi

# Check dependencies
echo -e "${YELLOW}Checking dependencies...${NC}"

# Check if Python packages are available
python3 -c "import pandas, matplotlib, seaborn, numpy" 2>/dev/null || {
    echo -e "${RED}Error: Required Python packages not found${NC}"
    echo "Please install: pip install pandas matplotlib seaborn numpy"
    exit 1
}

# Check if bc is available for calculations
which bc > /dev/null || {
    echo -e "${RED}Error: 'bc' calculator not found${NC}"
    echo "Please install bc: apt-get install bc"
    exit 1
}

echo -e "${GREEN}Dependencies check passed${NC}"
echo

# Step 1: Run comprehensive benchmarks
echo -e "${BLUE}Step 1: Running comprehensive benchmarks...${NC}"
cd scripts
if ./comprehensive_benchmark.sh; then
    echo -e "${GREEN}Benchmarks completed successfully${NC}"
else
    echo -e "${RED}Benchmarks failed${NC}"
    exit 1
fi
cd ..

# Find the latest results directory
LATEST_RESULTS=$(find results -name "benchmark_*" -type d | sort | tail -1)
if [[ -z "$LATEST_RESULTS" ]]; then
    echo -e "${RED}Error: No benchmark results found${NC}"
    exit 1
fi

echo -e "${GREEN}Latest results found: $LATEST_RESULTS${NC}"
echo

# Step 2: Run analysis
echo -e "${BLUE}Step 2: Analyzing benchmark results...${NC}"
ANALYSIS_OUTPUT="$LATEST_RESULTS/analysis"
mkdir -p "$ANALYSIS_OUTPUT"

cd analysis
if python3 analyze_results.py "../$LATEST_RESULTS/benchmark_results.csv" --output-dir "../$ANALYSIS_OUTPUT"; then
    echo -e "${GREEN}Analysis completed successfully${NC}"
else
    echo -e "${RED}Analysis failed${NC}"
    exit 1
fi
cd ..

# Step 3: Generate summary report
echo -e "${BLUE}Step 3: Generating summary report...${NC}"

SUMMARY_FILE="$LATEST_RESULTS/ANALYSIS_SUMMARY.md"

cat > "$SUMMARY_FILE" << EOF
# CUDASTF vs OpenMP Benchmark Analysis Summary

**Analysis Date:** $(date)
**Results Directory:** $LATEST_RESULTS

## Quick Access

### Key Files
- **Raw Results:** \`benchmark_results.csv\`
- **Statistical Summary:** \`analysis/statistical_summary.csv\`
- **Detailed Statistics:** \`analysis/detailed_statistics.csv\`
- **Performance Report:** \`analysis/performance_report.md\`

### Visualizations
- **Performance Comparison:** \`analysis/performance_comparison_by_task.png\`
- **Speedup Analysis:** \`analysis/speedup_analysis.png\`
- **Kernel Performance:** \`analysis/performance_by_kernel.png\`
- **Scaling Analysis:** \`analysis/scaling_analysis.png\`
- **Performance Heatmaps:** \`analysis/heatmap_*.png\`

## Results Overview

EOF

# Add basic statistics to summary
if [[ -f "$LATEST_RESULTS/benchmark_results.csv" ]]; then
    echo "### Benchmark Statistics" >> "$SUMMARY_FILE"
    echo >> "$SUMMARY_FILE"
    
    # Count total tests
    TOTAL_TESTS=$(tail -n +2 "$LATEST_RESULTS/benchmark_results.csv" | wc -l)
    SUCCESS_TESTS=$(tail -n +2 "$LATEST_RESULTS/benchmark_results.csv" | grep "SUCCESS" | wc -l)
    
    echo "- **Total Tests Run:** $TOTAL_TESTS" >> "$SUMMARY_FILE"
    echo "- **Successful Tests:** $SUCCESS_TESTS" >> "$SUMMARY_FILE"
    echo "- **Success Rate:** $(echo "scale=2; $SUCCESS_TESTS * 100 / $TOTAL_TESTS" | bc)%" >> "$SUMMARY_FILE"
    echo >> "$SUMMARY_FILE"
    
    # Implementation breakdown
    echo "### Implementation Breakdown" >> "$SUMMARY_FILE"
    echo >> "$SUMMARY_FILE"
    tail -n +2 "$LATEST_RESULTS/benchmark_results.csv" | grep "SUCCESS" | cut -d',' -f1 | sort | uniq -c | while read count impl; do
        echo "- **$impl:** $count successful tests" >> "$SUMMARY_FILE"
    done
    echo >> "$SUMMARY_FILE"
fi

# Add quick performance insights
if [[ -f "$LATEST_RESULTS/analysis/performance_report.md" ]]; then
    echo "### Key Findings" >> "$SUMMARY_FILE"
    echo >> "$SUMMARY_FILE"
    echo "See detailed analysis in \`analysis/performance_report.md\`" >> "$SUMMARY_FILE"
    echo >> "$SUMMARY_FILE"
fi

cat >> "$SUMMARY_FILE" << EOF

## How to Use These Results

1. **Start with the Performance Report:** Read \`analysis/performance_report.md\` for executive summary
2. **Review Visualizations:** Check PNG files in \`analysis/\` directory for visual insights
3. **Dive into Data:** Examine CSV files for detailed numerical analysis
4. **Identify Bottlenecks:** Focus on scenarios where CUDASTF underperforms

## Next Steps

Based on the analysis results, consider:
- Optimizing CUDASTF implementation for identified bottlenecks
- Investigating memory management patterns
- Analyzing task scheduling efficiency
- Profiling GPU utilization patterns

EOF

echo -e "${GREEN}Summary report generated: $SUMMARY_FILE${NC}"
echo

# Step 4: Display results summary
echo -e "${BLUE}Step 4: Results Summary${NC}"
echo -e "${CYAN}===========================================${NC}"
echo -e "${GREEN}Analysis Complete!${NC}"
echo
echo -e "${YELLOW}Results Location:${NC} $LATEST_RESULTS"
echo
echo -e "${YELLOW}Key Files:${NC}"
echo "  📊 Raw Data:           $LATEST_RESULTS/benchmark_results.csv"
echo "  📈 Analysis Report:    $LATEST_RESULTS/analysis/performance_report.md"
echo "  📋 Summary:            $LATEST_RESULTS/ANALYSIS_SUMMARY.md"
echo "  🎨 Visualizations:     $LATEST_RESULTS/analysis/*.png"
echo
echo -e "${YELLOW}Quick Commands:${NC}"
echo "  View summary:          cat $LATEST_RESULTS/ANALYSIS_SUMMARY.md"
echo "  View performance:      cat $LATEST_RESULTS/analysis/performance_report.md"
echo "  Open visualizations:   ls $LATEST_RESULTS/analysis/*.png"
echo
echo -e "${CYAN}===========================================${NC}"

# Optional: Open results directory
if command -v nautilus &> /dev/null; then
    echo -e "${BLUE}Opening results directory in file manager...${NC}"
    nautilus "$LATEST_RESULTS" &
elif command -v xdg-open &> /dev/null; then
    echo -e "${BLUE}Opening results directory...${NC}"
    xdg-open "$LATEST_RESULTS" &
fi

echo -e "${GREEN}Complete analysis finished successfully!${NC}"
