#!/bin/bash

# Comprehensive benchmark script for CUDASTF vs OpenMP comparison
# Collects detailed performance data for analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="../results/benchmark_${TIMESTAMP}"
WARMUP_RUNS=2
MEASUREMENT_RUNS=5

echo -e "${GREEN}=== Comprehensive CUDASTF vs OpenMP Benchmark ===${NC}"
echo "Results will be saved to: $RESULTS_DIR"

# Create results directory
mkdir -p "$RESULTS_DIR"

# Check dependencies
if [[ ! -d ../../deps ]]; then
    echo -e "${RED}Error: deps directory not found. Run ./get_deps.sh first.${NC}"
    exit 1
fi

source ../../deps/env.sh

# Test configurations
declare -a task_types=(
    "trivial"
    "no_comm"
    "stencil_1d"
    "stencil_1d_periodic"
    "dom"
    "tree"
    "fft"
    "nearest"
    "spread -period 2"
    "random_nearest"
)

declare -a kernel_configs=(
    ""
    "-kernel compute_bound -iter 1024"
    "-kernel memory_bound -iter 1024 -scratch $((64*16))"
    "-kernel load_imbalance -iter 1024 -imbalance 0.1"
    "-output 1024"
)

declare -a kernel_names=(
    "default"
    "compute_bound"
    "memory_bound"
    "load_imbalance"
    "communication_bound"
)

declare -a step_sizes=(5 10 15 20 25 30)

# Function to run single benchmark
run_single_benchmark() {
    local impl=$1
    local binary=$2
    local worker_flag=$3
    local task_type=$4
    local kernel_config=$5
    local kernel_name=$6
    local steps=$7
    local run_id=$8
    
    local cmd="../../$impl/$binary -steps $steps -type $task_type $kernel_config"
    if [[ -n "$worker_flag" ]]; then
        cmd="$cmd $worker_flag"
    fi
    
    echo "Running: $impl - $task_type - $kernel_name - steps:$steps - run:$run_id"
    
    # Capture timing and output
    local start_time=$(date +%s.%N)
    local output_file="$RESULTS_DIR/${impl}_${task_type}_${kernel_name}_steps${steps}_run${run_id}.out"
    local error_file="$RESULTS_DIR/${impl}_${task_type}_${kernel_name}_steps${steps}_run${run_id}.err"
    
    if timeout 300 bash -c "$cmd" > "$output_file" 2> "$error_file"; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l)
        
        # Extract performance metrics from output
        local total_time=$(grep "Total time:" "$output_file" | awk '{print $3}' || echo "N/A")
        local task_throughput=$(grep "Task throughput:" "$output_file" | awk '{print $3}' || echo "N/A")
        
        # Log to CSV
        echo "$impl,$task_type,$kernel_name,$steps,$run_id,$duration,$total_time,$task_throughput,SUCCESS" >> "$RESULTS_DIR/benchmark_results.csv"
        
        return 0
    else
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l)
        echo "$impl,$task_type,$kernel_name,$steps,$run_id,$duration,N/A,N/A,FAILED" >> "$RESULTS_DIR/benchmark_results.csv"
        echo -e "${RED}FAILED: $cmd${NC}"
        return 1
    fi
}

# Function to run implementation benchmarks
run_implementation_benchmarks() {
    local impl=$1
    local binary=$2
    local worker_flag=$3
    
    echo -e "${YELLOW}Running $impl benchmarks...${NC}"
    
    local total_tests=0
    local passed_tests=0
    
    for task_type in "${task_types[@]}"; do
        for i in "${!kernel_configs[@]}"; do
            local kernel_config="${kernel_configs[$i]}"
            local kernel_name="${kernel_names[$i]}"
            
            for steps in "${step_sizes[@]}"; do
                # Warmup runs
                for warmup in $(seq 1 $WARMUP_RUNS); do
                    echo "  Warmup $warmup/$WARMUP_RUNS"
                    run_single_benchmark "$impl" "$binary" "$worker_flag" "$task_type" "$kernel_config" "$kernel_name" "$steps" "warmup$warmup" > /dev/null 2>&1 || true
                done
                
                # Measurement runs
                for run in $(seq 1 $MEASUREMENT_RUNS); do
                    total_tests=$((total_tests + 1))
                    if run_single_benchmark "$impl" "$binary" "$worker_flag" "$task_type" "$kernel_config" "$kernel_name" "$steps" "$run"; then
                        passed_tests=$((passed_tests + 1))
                    fi
                done
            done
        done
    done
    
    echo -e "${GREEN}$impl: $passed_tests/$total_tests tests passed${NC}"
    return $((total_tests - passed_tests))
}

# Initialize CSV file
echo "implementation,task_type,kernel,steps,run_id,wall_time,total_time,task_throughput,status" > "$RESULTS_DIR/benchmark_results.csv"

# Build implementations
echo -e "${YELLOW}Building implementations...${NC}"

echo "Building OpenMP..."
cd ../../openmp
make clean > /dev/null 2>&1
if ! make -j$(nproc) > /dev/null 2>&1; then
    echo -e "${RED}Failed to build OpenMP${NC}"
    exit 1
fi
cd ../cudastf_vs_openmp/scripts

echo "Building CUDASTF..."
cd ../../cudastf
make clean > /dev/null 2>&1
if ! make -j$(nproc) > /dev/null 2>&1; then
    echo -e "${RED}Failed to build CUDASTF${NC}"
    exit 1
fi
cd ../cudastf_vs_openmp/scripts

# Run benchmarks
total_failed=0

# OpenMP benchmarks
if [[ "$USE_OPENMP" == "1" ]]; then
    export LD_LIBRARY_PATH=/usr/local/clang/lib:$LD_LIBRARY_PATH
    run_implementation_benchmarks "openmp" "main" "-worker 2"
    total_failed=$((total_failed + $?))
else
    echo -e "${YELLOW}OpenMP benchmarks skipped (USE_OPENMP != 1)${NC}"
fi

# CUDASTF benchmarks
run_implementation_benchmarks "cudastf" "main" ""
total_failed=$((total_failed + $?))

# Generate summary
echo -e "${BLUE}Generating benchmark summary...${NC}"
python3 - << EOF
import pandas as pd
import sys

try:
    df = pd.read_csv('$RESULTS_DIR/benchmark_results.csv')
    
    # Summary statistics
    summary = df.groupby(['implementation', 'task_type', 'kernel']).agg({
        'wall_time': ['mean', 'std', 'min', 'max'],
        'total_time': lambda x: x.replace('N/A', None).astype(float).mean(),
        'task_throughput': lambda x: x.replace('N/A', None).astype(float).mean(),
        'status': lambda x: (x == 'SUCCESS').sum()
    }).round(6)
    
    summary.to_csv('$RESULTS_DIR/benchmark_summary.csv')
    print("Summary saved to benchmark_summary.csv")
    
except Exception as e:
    print(f"Error generating summary: {e}")
    sys.exit(1)
EOF

echo
if [[ $total_failed -eq 0 ]]; then
    echo -e "${GREEN}All benchmarks completed successfully!${NC}"
    echo "Results saved to: $RESULTS_DIR"
else
    echo -e "${RED}$total_failed tests failed${NC}"
    echo "Results saved to: $RESULTS_DIR"
    exit 1
fi
