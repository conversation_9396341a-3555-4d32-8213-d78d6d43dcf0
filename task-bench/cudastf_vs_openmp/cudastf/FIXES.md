# CUDA STF Task Bench 修复说明

## 修复的问题

### 1. Spread 依赖模式参数错误

**问题**：
```bash
./main -type spread -steps 2 -width 2
# 错误：Graph type "spread" requires a period that is at most 1
```

**原因**：
- `spread` 依赖模式需要 `-period` 参数
- Period 必须满足约束：`period ≤ (width + radix - 1) / radix`
- 对于 width=2, radix=3（默认），最大 period = (2+3-1)/3 = 1

**修复**：
```bash
# 正确的命令
./main -type spread -steps 2 -width 4 -period 2
```

**测试套件修复**：
```bash
# 修改前
run_test "Spread pattern" "./main -type spread -steps 2 -width 2"

# 修改后  
run_test "Spread pattern" "./main -type spread -steps 2 -width 4 -period 2"
```

### 2. Memory-bound 内核缺少 Scratch 参数

**问题**：
```bash
./main -kernel memory_bound -iter 100 -steps 2 -width 2
# 错误：Assertion `scratch_ptr != NULL' failed
```

**原因**：
- `memory_bound` 内核需要 scratch 缓冲区来执行内存操作
- 必须使用 `-scratch [INT]` 参数指定缓冲区大小

**修复**：
```bash
# 正确的命令
./main -kernel memory_bound -iter 100 -scratch 1024 -steps 2 -width 2
```

**测试套件修复**：
```bash
# 修改前
run_test "Memory-bound kernel" "./main -kernel memory_bound -iter 100 -steps 2 -width 2"

# 修改后
run_test "Memory-bound kernel" "./main -kernel memory_bound -iter 100 -scratch 1024 -steps 2 -width 2"
```

### 3. 不可用内核的处理

**问题**：
以下内核在当前环境中不可用：
- `compute_dgemm`：需要 BLAS 库支持
- `memory_daxpy`：需要 BLAS 库支持  
- `io_bound`：未实现（只有 `assert(false)`）

**修复**：
在测试套件中跳过这些测试，并添加说明：

```bash
# Test fallback kernels (these require BLAS support or are not implemented)
echo "Note: Skipping BLAS-dependent and unimplemented kernels:"
echo "  - compute_dgemm (requires BLAS)"
echo "  - memory_daxpy (requires BLAS)" 
echo "  - io_bound (not implemented)"
```

## 内核参数要求总结

| 内核类型 | 必需参数 | 可选参数 | 说明 |
|---------|---------|---------|------|
| `empty` | 无 | 无 | 空操作 |
| `compute_bound` | `-iter` | 无 | CPU 计算密集型 |
| `compute_bound2` | `-iter` | 无 | CPU 计算密集型变体 |
| `memory_bound` | `-iter`, `-scratch` | `-sample` | 内存密集型 |
| `busy_wait` | `-iter` | 无 | 忙等待 |
| `load_imbalance` | `-iter`, `-imbalance` | 无 | 负载不平衡 |
| `compute_dgemm` | `-iter`, `-scratch` | 无 | 需要 BLAS |
| `memory_daxpy` | `-iter`, `-scratch` | 无 | 需要 BLAS |
| `io_bound` | 无 | 无 | 未实现 |

## 依赖模式参数要求

| 依赖模式 | 必需参数 | 约束条件 |
|---------|---------|---------|
| `trivial` | 无 | 无 |
| `no_comm` | 无 | 无 |
| `stencil_1d` | 无 | 无 |
| `stencil_1d_periodic` | 无 | 无 |
| `dom` | 无 | 无 |
| `tree` | 无 | 无 |
| `fft` | 无 | 无 |
| `all_to_all` | 无 | 无 |
| `nearest` | `-radix` | 无 |
| `spread` | `-period` | `period ≤ (width + radix - 1) / radix` |
| `random_nearest` | `-period` | 无 |

## 测试结果

修复后的测试套件结果：
```
Tests passed: 29 / 29
Status: ALL TESTS PASSED ✅
```

所有可用的功能都已正确实现并通过测试。
