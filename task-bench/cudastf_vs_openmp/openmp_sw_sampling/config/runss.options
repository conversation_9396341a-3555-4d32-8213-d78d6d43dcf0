-r
/root/stf_exp/task-bench/cudastf_vs_openmp/openmp_sw_sampling
--stack-stitching
--data-limit-mb=1000
--disk-space-limit=0
--mrte-type=java,python
--stack-unwind-limit=8388608
--itt-config=frame
--itt-config=task,event,counter
--stackwalk=offline
--mrte-mode=auto
--type=cpu:counters:nostack
--type=cpu:stack
--interval=10
--pmu-type
--event-config=CPU_CLK_UNHALTED.THREAD,CPU_CLK_UNHALTED.REF_TSC:sample:sa=2900000,INST_RETIRED.ANY:sample:sa=2900000,CPU_CLK_UNHALTED.DISTRIBUTED,UOPS_RETIRED.RETIRE_SLOTS:sample:sa=2000003
--collector=perf
--count
--
../openmp/main
-steps
5
-width
4
-type
stencil_1d
-kernel
compute_bound
