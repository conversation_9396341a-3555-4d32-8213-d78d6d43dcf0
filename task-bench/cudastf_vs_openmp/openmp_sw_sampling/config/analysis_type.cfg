<?xml version="1.0" encoding="UTF-8"?>
<bag xmlns:boolean="http://www.w3.org/2001/XMLSchema#boolean" xmlns:double="http://www.intel.com/2001/XMLSchema#double" xmlns:exsl="http://exslt.org/common" xmlns:int="http://www.w3.org/2001/XMLSchema#int" xmlns:str="http://exslt.org/strings" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
 <internal>
  <name>%HotspotsAtypeName</name>
  <shortName>%HotspotsAtypeShortName</shortName>
  <description>%HotspotsAtypeDescription</description>
  <shortDescription>%HotspotsAtypeShortDescription</shortDescription>
  <abbreviation>hs</abbreviation>
  <alias>hotspots</alias>
  <int:schemaVersion>1</int:schemaVersion>
  <int:contentVersion>1</int:contentVersion>
  <iconClass>fire solid</iconClass>
  <property name="helpId">configs.analysis_type-hotspots_f1101</property>
 </internal>
 <prerequisites xsl:version="1.0" exsl:keep_exsl_namespace="" syntax="norules">
  <xsl:copy-of select="document('config://analysis_type/include/require_connection.xsl?connections=group_generic,tcp')"/>
 </prerequisites>
 <knobs xsl:version="1.0" exsl:keep_exsl_namespace="" syntax="norules">
  <xsl:variable name="pmuConditionsExtended" select="document('config://include/pmu_variables_extended.xsl')"/>
  <xsl:variable name="isExtendedIsaSupport" select="$pmuConditionsExtended//variables/isExtendedIsaSupport"/>
  <stringKnob id="initialViewpoint" displayName="%InitialViewpoint" boolean:visible="false">
   <defaultValue>%HotspotsByCPUUsageViewpointName</defaultValue>
  </stringKnob>
  <stringKnob id="allowedViewpoints" boolean:visible="false">
   <defaultValue>%HotspotsByCPUUsageViewpointName,%TasksOverviewViewpointName</defaultValue>
  </stringKnob>
  <xsl:variable name="knobsParams">
   <params samplingIntervalApplyKnob="samplingMode" samplingIntervalApplyKnobValue="hw" pmuSamplingIntervalDescription="SamplingIntervalDescriptionOnHotspots"/>
  </xsl:variable>
  <xsl:variable name="knobsParamsName">
   <xsl:text>config://analysis_type/include/knobs.xsl?</xsl:text>
   <xsl:for-each select="exsl:node-set($knobsParams)//@*">
    <xsl:value-of select="concat(name(), '=', .)"/>
    <xsl:text>&amp;</xsl:text>
   </xsl:for-each>
  </xsl:variable>
  <xsl:variable name="commonKnobs" select="document($knobsParamsName)"/>
  <xsl:choose>
   <xsl:when test="exsl:ctx('targetOS', '') = 'QNX' or exsl:ctx('targetOS', '') = 'MacOSX'">
    <enumKnob id="samplingMode" displayName="%SamplingMode" cliName="sampling-mode" boolean:visible="false">
     <description>%SamplingModeDescription</description>
     <values>
      <value displayName="%HardwareSampling" cliName="hw">hw</value>
      <defaultValue>hw</defaultValue>
     </values>
    </enumKnob>
    <xsl:copy-of select="$commonKnobs//knobs/doubleKnob[@id='samplingInterval']"/>
    <booleanKnob id="enableStackCollect" displayName="%EnableStackCollection" cliName="enable-stack-collection">
     <xsl:if test="not(exsl:is_experimental('sep-lbr'))">
      <xsl:attribute name="boolean:visible">false</xsl:attribute>
     </xsl:if>
     <description>%EnableStackCollectionDescription</description>
     <boolean:defaultValue>false</boolean:defaultValue>
    </booleanKnob>
   </xsl:when>
   <xsl:when test="$isExtendedIsaSupport='true'">
    <enumKnob id="samplingMode" cliName="sampling-mode" boolean:visible="true">
     <description>%HardwareSamplingDescription</description>
     <values>
      <value displayName="%HardwareSampling" cliName="hw">hw</value>
      <defaultValue>hw</defaultValue>
     </values>
    </enumKnob>
    <xsl:copy-of select="$commonKnobs//knobs/doubleKnob[@id='samplingInterval']"/>
    <booleanKnob id="enableStackCollect" displayName="%EnableStackCollection" cliName="enable-stack-collection">
     <xsl:attribute name="boolean:visible">false</xsl:attribute>
     <description>%EnableStackCollectionDescription</description>
     <boolean:defaultValue>true</boolean:defaultValue>
    </booleanKnob>
   </xsl:when>
   <xsl:otherwise>
    <groupKnob id="groupForCustomControl">
     <knobProperty name="knob_control_id">hotspotsGroup</knobProperty>
     <knobs>
      <enumKnob id="samplingMode" displayName="%SamplingMode" cliName="sampling-mode" boolean:visible="true">
       <description>%SamplingModeDescription</description>
       <values>
        <value displayName="%SoftwareSampling" cliName="sw">sw</value>
        <value displayName="%HardwareSampling" cliName="hw">hw</value>
        <defaultValue>sw</defaultValue>
       </values>
      </enumKnob>
      <xsl:copy-of select="$commonKnobs//knobs/doubleKnob[@id='samplingInterval']"/>
      <booleanKnob id="enableStackCollect" displayName="%EnableStackCollection" cliName="enable-stack-collection">
       <xsl:if test="not(exsl:is_experimental('sep-lbr')) and exsl:ctx('targetOS', '') = 'MacOSX'">
        <xsl:attribute name="boolean:visible">false</xsl:attribute>
       </xsl:if>
       <description>%EnableStackCollectionDescription</description>
       <boolean:defaultValue>false</boolean:defaultValue>
      </booleanKnob>
      <xsl:copy-of select="$commonKnobs//knobs/enumKnob[@id='stackSizeCollect']"/>
     </knobs>
    </groupKnob>
   </xsl:otherwise>
  </xsl:choose>
  <doubleKnob id="slowGoodFrameThreshold" displayName="%SlowGoodFrameThreshold" cliName="slow-frames-threshold" boolean:visible="false">
   <description>%SlowGoodFrameThresholdDescription</description>
   <double:min>0.01</double:min>
   <double:max>1024000</double:max>
   <double:defaultValue>40</double:defaultValue>
  </doubleKnob>
  <doubleKnob id="goodFastFrameThreshold" displayName="%GoodFastFrameThreshold" cliName="fast-frames-threshold" boolean:visible="false">
   <description>%GoodFastFrameThresholdDescription</description>
   <double:min>0.01</double:min>
   <double:max>1024000</double:max>
   <double:defaultValue>100</double:defaultValue>
  </doubleKnob>
  <xsl:if test="exsl:ctx('targetOS', '') != 'Android'">
   <booleanKnob id="enableCharacterizationInsights" displayName="%EnableCharacterizationInsights" cliName="enable-characterization-insights">
    <boolean:defaultValue>true</boolean:defaultValue>
    <description>%EnableCharacterizationInsightsDescription</description>
   </booleanKnob>
  </xsl:if>
 </knobs>
 <analysis xsl:version="1.0" str:keep_str_namespace="" exsl:keep_exsl_namespace="" syntax="norules">
  <xsl:variable name="minMajorVersion" select="number(5)"/>
  <xsl:variable name="minMinorVersion" select="number(10)"/>
  <xsl:variable name="sepVersion" select="string(exsl:ctx('SEPDriverVersion', ''))"/>
  <xsl:variable name="currentSepVersions" select="str:tokenize($sepVersion, '.')"/>
  <xsl:variable name="pmuConditions" select="document('config://include/pmu_variables.xsl')"/>
  <xsl:variable name="isHybridPMU" select="$pmuConditions//variables/isHybridPMU"/>
  <xsl:variable name="useEventBasedCounts" select="(exsl:ctx('samplingMode', 'sw')='sw' or (number($currentSepVersions[1]) &gt; $minMajorVersion) or        (number($currentSepVersions[1]) = $minMajorVersion and number($currentSepVersions[2]) &gt; $minMinorVersion)) and ($isHybridPMU != 'true')"/>
  <xsl:variable name="events" select="document(concat('config://analysis_type/include/common_events.xsl?', 'useEventBasedCounts=', $useEventBasedCounts, '&amp;isHotspots=true'))"/>
  <xsl:variable name="mainEvents" select="$events//events/cpi"/>
  <xsl:variable name="fpuEvents" select="$events//events/fpu"/>
  <xsl:variable name="retiredEvents" select="$events//events/retired"/>
  <xsl:variable name="retiredEventsPM" select="$events//events/retired_perf_metrics"/>
  <xsl:variable name="pmuCommon" select="document('config://include/pmu_common.xsl')"/>
  <xsl:variable name="perfMetricsPossible" select="$pmuCommon//variables/perfMetricsPossible"/>
  <collector id="runss">
   <xsl:choose>
    <xsl:when test="exsl:ctx('samplingMode', 'sw')='sw'">
     <xsl:if test="not(exsl:ctx('isTPSSAvailable', 0)) and not(exsl:ctx('isPtraceAvailable', 0)) and not(exsl:ctx('targetOS', '') = 'QNX')">
      <xsl:value-of select="exsl:error('%RunssHotspotsNotSupported')"/>
     </xsl:if>
     <xsl:if test="exsl:ctx('isPtraceScopeLimited', 0)">
      <xsl:value-of select="exsl:error('%RunssPtraceScopeLimited')"/>
     </xsl:if>
     <xsl:if test="exsl:ctx('targetOS', '') = 'MacOSX'">
      <xsl:value-of select="exsl:error('%ThisAnalysisTypeIsNotApplicable')"/>
     </xsl:if>
     <collectorKnob knob="collectSamplesMode">stack</collectorKnob>
     <collectorKnob knob="samplingInterval">10</collectorKnob>
     <collectorKnob knob="collectUserTasksEventsCountersMode">true</collectorKnob>
     <xsl:if test="exsl:ctx('isPytraceAvailable', 0)">
      <xsl:choose>
       <xsl:when test="exsl:ctx('targetOS', '') = 'Windows'">
        <collectorKnob knob="mrteType">java,dotnet,python</collectorKnob>
       </xsl:when>
       <xsl:when test="exsl:ctx('targetOS', '') = 'Linux'">
        <collectorKnob knob="mrteType">java,python</collectorKnob>
       </xsl:when>
      </xsl:choose>
     </xsl:if>
     <collectorKnob knob="collectOpenMPRegions">
      <xsl:value-of select="exsl:ctx('analyzeOpenMPRegions', 0)"/>
     </collectorKnob>
     <xsl:if test="exsl:ctx('enableCharacterizationInsights', 0)">
      <xsl:variable name="isSEPFlow" select="exsl:ctx('isSEPDriverAvailable', 0) and (not(exsl:ctx('usePerf', 0)) or exsl:ctx('targetOS', '') = 'Windows' or exsl:ctx('targetOS', '') = 'MacOSX' or exsl:ctx('targetOS', '') = 'FreeBSD')"/>
      <xsl:variable name="isPerfFlow" select="(exsl:ctx('targetOS', '') = 'Linux' or exsl:ctx('targetOS', '') = 'Android') and exsl:ctx('LinuxPerfCredentials', 'NotAvailable')!='NotAvailable'                        and contains(exsl:ctx('LinuxPerfCapabilities', ''), 'format')"/>
      <xsl:variable name="isPMUAvailable" select="exsl:ctx('PerfmonVersion', '4') &gt; '1' and ($isSEPFlow or $isPerfFlow)"/>
      <xsl:variable name="isPermissionInsufficient" select="exsl:ctx('targetOS')='Windows' and not(exsl:ctx('AdministratorPrivileges', 'false'))"/>
      <xsl:choose>
       <xsl:when test="not($isPMUAvailable) or $isPermissionInsufficient">
        <xsl:if test="not($isPMUAvailable)">
         <xsl:value-of select="exsl:warning('%HWInsightsNotAvailableWarningTpss')"/>
        </xsl:if>
        <xsl:if test="$isPermissionInsufficient">
         <xsl:value-of select="exsl:warning('%HWInsightsNotAvailablePermissionWarningTpss')"/>
        </xsl:if>
       </xsl:when>
       <xsl:otherwise>
        <xsl:variable name="fullEventList">
         <xsl:value-of select="$mainEvents"/>
         <xsl:if test="$fpuEvents and $fpuEvents != ''">
          <xsl:text>,</xsl:text>
          <xsl:value-of select="$fpuEvents"/>
         </xsl:if>
         <xsl:choose>
          <xsl:when test="$perfMetricsPossible = 'true'">
           <xsl:if test="$retiredEventsPM and $retiredEventsPM != ''">
            <xsl:text>,</xsl:text>
            <xsl:value-of select="$retiredEventsPM"/>
           </xsl:if>
          </xsl:when>
          <xsl:otherwise>
           <xsl:if test="$retiredEvents and $retiredEvents != ''">
            <xsl:text>,</xsl:text>
            <xsl:value-of select="$retiredEvents"/>
           </xsl:if>
          </xsl:otherwise>
         </xsl:choose>
        </xsl:variable>
        <collectorKnob knob="pmuEventConfig">
         <xsl:value-of select="exsl:merge_pmu_events($fullEventList)"/>
        </collectorKnob>
        <xsl:choose>
         <xsl:when test="$perfMetricsPossible = 'true'">
          <boolean:collectorKnob knob="useAggregatedCounting">true</boolean:collectorKnob>
          <boolean:collectorKnob knob="usePerfMetrics">true</boolean:collectorKnob>
         </xsl:when>
         <xsl:otherwise>
          <boolean:collectorKnob knob="useCountingMode">true</boolean:collectorKnob>
         </xsl:otherwise>
        </xsl:choose>
       </xsl:otherwise>
      </xsl:choose>
     </xsl:if>
     <boolean:collectorKnob knob="collectSWHotspots">true</boolean:collectorKnob>
    </xsl:when>
    <xsl:otherwise>
     <xsl:attribute name="id">runsa</xsl:attribute>
     <xsl:if test="exsl:ctx('PMU', '') = ''">
      <xsl:value-of select="exsl:error('%UnknownPMUForAT')"/>
     </xsl:if>
     <collectorKnob knob="useEventBasedCounts">
      <xsl:value-of select="$useEventBasedCounts"/>
     </collectorKnob>
     <collectorKnob knob="collectUserTasksEventsCountersMode">true</collectorKnob>
     <collectorKnob knob="eventMode">all</collectorKnob>
     <collectorKnob knob="enableStackCollection">
      <xsl:value-of select="exsl:ctx('enableStackCollect', 0)"/>
     </collectorKnob>
     <xsl:copy-of select="document('config://analysis_type/include/knobs.xsl')//knobs/stackSizeKnobLogic/*"/>
     <boolean:collectorKnob knob="enableCSwitch">false</boolean:collectorKnob>
     <boolean:collectorKnob knob="cpuGpuUsageData">true</boolean:collectorKnob>
     <xsl:variable name="usePerfMetrics" select="$perfMetricsPossible = 'true' and not(exsl:ctx('enableStackCollect', 0))"/>
     <xsl:variable name="fullEventList">
      <xsl:value-of select="$mainEvents"/>
      <xsl:choose>
       <xsl:when test="$usePerfMetrics">
        <xsl:if test="$retiredEventsPM and $retiredEventsPM != ''">
         <xsl:text>,</xsl:text>
         <xsl:value-of select="$retiredEventsPM"/>
        </xsl:if>
       </xsl:when>
       <xsl:otherwise>
        <xsl:if test="$retiredEvents and $retiredEvents != ''">
         <xsl:text>,</xsl:text>
         <xsl:value-of select="$retiredEvents"/>
        </xsl:if>
       </xsl:otherwise>
      </xsl:choose>
      <xsl:if test="exsl:ctx('enableCharacterizationInsights', 0)">
       <xsl:if test="$fpuEvents and $fpuEvents != ''">
        <xsl:text>,</xsl:text>
        <xsl:value-of select="$fpuEvents"/>
       </xsl:if>
      </xsl:if>
     </xsl:variable>
     <collectorKnob knob="pmuEventConfig">
      <xsl:value-of select="exsl:merge_pmu_events($fullEventList)"/>
     </collectorKnob>
     <collectorKnob knob="pmuSamplingInterval">
      <xsl:value-of select="format-number(exsl:ctx('samplingInterval', 1), '#.####')"/>
     </collectorKnob>
     <boolean:collectorKnob knob="isUArchUsageAvailable">true</boolean:collectorKnob>
     <collectorKnob knob="usePerfMetrics">
      <xsl:value-of select="$usePerfMetrics"/>
     </collectorKnob>
     <xsl:if test="exsl:is_experimental('ehfi')">
      <collectorKnob knob="analyzeEHFIClasses">true</collectorKnob>
     </xsl:if>
    </xsl:otherwise>
   </xsl:choose>
  </collector>
 </analysis>
</bag>
